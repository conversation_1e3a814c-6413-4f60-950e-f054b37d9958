import json
from datetime import datetime
from functools import cache

import responses
import responses.matchers
from requests import HTTPError

from apps.connectors.health_checks.components.component import (
    ComponentType,
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations import IntegrationActionType
from apps.connectors.integrations.actions.event_sync import EventSyncArgs
from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    SignInLogsByUserIdArgs,
)
from apps.connectors.integrations.actions.user.user_login import (
    UserIdentifierArgs,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas import UserIdentifier, ocsf
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.authentication import (
    map_factor_type,
    map_status_and_disposition,
    normalize_authentication_log,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.event_sync import (
    CiscoDuoV1EventSync,
    convert_to_ocsf,
    map_alert_type,
    map_ocsf_severity,
    normalize_event,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import (
    paginate_authentication_logs,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.bookmarks import (
    CiscoDuoV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ConnectionHealthCheck,
    ReadAuthenticationLogs,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


@cache
def load_data(filename):
    """Load test data from JSON files."""
    path = "apps/connectors/tests/integrations/data/cisco_duo"
    with open(f"{path}/{filename}", "r") as f:
        return json.load(f)


def get_result_object(id=1):
    return {
        "browsers": [
            {
                "browser_family": "Chrome",
                "browser_version": "91.0.4472.77",
                "flash_version": "uninstalled",
                "java_version": "uninstalled",
                "last_used": **********,
            },
            {
                "browser_family": "Safari",
                "browser_version": "14.1",
                "flash_version": "uninstalled",
                "java_version": "uninstalled",
                "last_used": **********,
            },
        ],
        "computer_sid": f"test_sid_{id}",
        "cpu_id": "",
        "device_id": "",
        "device_identifier": "3FA47335-1976-3BED-8286-D3F1ABCDEA13",
        "device_identifier_type": "hardware_uuid",
        "device_name": f"test_host_name_{id}",
        "device_udid": "",
        "device_username": f"user{id} MacBook Air/mba22915",
        "device_username_type": "os_username",
        "disk_encryption_status": "On",
        "domain_sid": "",
        "email": "<EMAIL>",
        "epkey": "EP18JX1A10AB102M2T2X",
        "firewall_status": "On",
        "hardware_uuid": "3FA47335-1976-3BED-8286-D3F1ABCDEA13",
        "health_app_client_version": "********",
        "health_data_last_collected": **********,
        "last_updated": "2018-04-13T03:48:50Z",
        "machine_guid": "",
        "model": "",
        "os_build": "19H1030",
        "os_family": "Mac OS X",
        "os_version": "10.11.7",
        "password_status": "Set",
        "security_agents": [
            {
                "security_agent": "Cisco AMP for Endpoints",
                "version": "********",
            }
        ],
        "trusted_endpoint": "yes",
        "type": "",
        "username": "ejennings",
    }


def normalized_object(id=1):
    return {
        "source_id": f"test_sid_{id}",
        "group_names": [],
        "hostname": f"test_host_name_{id}",
        "fqdns": [],
        "ip_addresses": [],
        "mac_addresses": [],
        "is_internet_facing": None,
        "internet_exposure": "unknown",
        "os": {
            "host_type": "workstation",
            "family": "mac",
            "name": "Mac OS X 10.11.7",
        },
        "owners": [
            {
                "email": "<EMAIL>",
                "name": f"user{id} MacBook Air/mba22915",
            }
        ],
        "aad_id": None,
        "criticality": "unknown",
        "last_seen": "2018-04-13T03:48:50Z",
        "source_data": get_result_object(id),
    }


def setup_responses():
    responses.get(
        "https://api-test_host.duosecurity.com/admin/v1/endpoints",
        match=[responses.matchers.query_param_matcher({"offset": 0, "limit": 100})],
        json={
            "stat": "OK",
            "response": [
                get_result_object(1),
                get_result_object(2),
            ],
            "metadata": {"next_offset": 3, "prev_offset": 2, "total_objects": 3},
        },
    )


def setup_pagination_responses():
    setup_responses()
    responses.get(
        "https://api-test_host.duosecurity.com/admin/v1/endpoints",
        match=[responses.matchers.query_param_matcher({"offset": 1, "limit": 100})],
        json={
            "stat": "OK",
            "response": [
                get_result_object(3),
            ],
            "metadata": {"next_offset": 3, "prev_offset": 2, "total_objects": 3},
        },
    )
    responses.get(
        "https://api-test_host.duosecurity.com/admin/v1/endpoints",
        match=[responses.matchers.query_param_matcher({"offset": 2, "limit": 100})],
        json={
            "stat": "OK",
            "response": [],
            "metadata": {"next_offset": 3, "prev_offset": 2, "total_objects": 0},
        },
    )


class CiscoDuoV1ApiTest(BaseTestCase):
    @responses.activate
    def test_get_endpoints(self):
        setup_pagination_responses()
        api = ConnectorFactory.get_api(technology_id="cisco_duo")
        devices = api.get_endpoints()
        self.assertEqual(
            devices,
            {
                "stat": "OK",
                "response": [
                    get_result_object(1),
                    get_result_object(2),
                ],
                "metadata": {"next_offset": 3, "prev_offset": 2, "total_objects": 3},
            },
        )

    @responses.activate
    def test_get_endpoints_fail(self):
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/endpoints", status=400
        )
        api = ConnectorFactory.get_api(technology_id="cisco_duo")
        self.assertRaises(Exception, api.get_endpoints)

    @responses.activate
    def test_get_authentication_logs_with_users(self):
        """Test getting authentication logs with users parameter."""
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json=load_data("authentication_logs_response.json"),
        )
        api = ConnectorFactory.get_api(technology_id="cisco_duo")
        result = api.get_authentication_logs(users="DAAAAAAAAAAAAAAAAAA1")

        self.assertEqual(result["stat"], "OK")
        self.assertIn("response", result)

    @responses.activate
    def test_get_authentication_logs_without_users(self):
        """Test getting authentication logs without users parameter (for event sync)."""
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [],
                    "metadata": {"next_offset": None},
                },
            },
        )
        api = ConnectorFactory.get_api(technology_id="cisco_duo")
        result = api.get_authentication_logs()

        self.assertEqual(result["stat"], "OK")
        self.assertIn("response", result)

    @responses.activate
    def test_get_authentication_logs_with_next_offset(self):
        """Test getting authentication logs with next_offset parameter."""
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [],
                    "metadata": {"next_offset": None},
                },
            },
        )
        api = ConnectorFactory.get_api(technology_id="cisco_duo")
        result = api.get_authentication_logs(next_offset=["1672574400000", "test-txid"])

        self.assertEqual(result["stat"], "OK")

    @responses.activate
    def test_get_authentication_logs_fail(self):
        """Test authentication logs API failure."""
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            status=400,
        )
        api = ConnectorFactory.get_api(technology_id="cisco_duo")
        self.assertRaises(Exception, api.get_authentication_logs, users="test_user")


class CiscoDuoV1IntegrationTest(BaseIntegrationTest):
    @responses.activate
    def test_get_hosts(self):
        setup_pagination_responses()
        self.assert_host_sync_results(
            [
                normalized_object(1),
                normalized_object(2),
                normalized_object(3),
            ]
        )


class CiscoDuoV1HealthCheckTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.connector = ConnectorFactory(technology_id="cisco_duo", version_id="v1")
        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_connection(self):
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/info/summary",
            json={
                "stat": "OK",
                "response": {
                    "admin_count": 21,
                    "edition": "Duo Premier",
                    "integration_count": 44,
                    "telephony_credits_remaining": 960,
                    "user_count": 862,
                    "user_pending_deletion_count": 9,
                },
            },
        )
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_connection_fail(self):
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/info/summary",
            body=HTTPError("invalid_credentials"),
        )
        health_check = ConnectionHealthCheck(
            integration=self.integration,
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class CiscoDuoV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()
        self.connector = ConnectorFactory(
            technology_id="cisco_duo", version_id="v1", enabled_actions=["host_sync"]
        )
        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_permissions(self):
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/info/summary",
            json={
                "stat": "OK",
                "response": {
                    "admin_count": 21,
                    "edition": "Duo Premier",
                    "integration_count": 44,
                    "telephony_credits_remaining": 960,
                    "user_count": 862,
                    "user_pending_deletion_count": 9,
                },
            },
        )

        setup_responses()
        components = HealthCheckComponent.get_components(connector=self.connector)
        permissions_checks_expected = [
            HealthCheckRequirement(
                name="Read all hosts",
                description="Read all hosts",
                value="Read all hosts",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]
        critical_checks_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]
        self.assert_components(
            components, [critical_checks_expected, permissions_checks_expected]
        )

    @responses.activate
    def test_permissions_failed(self):
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/info/summary",
            json={
                "stat": "OK",
                "response": {
                    "admin_count": 21,
                    "edition": "Duo Premier",
                    "integration_count": 44,
                    "telephony_credits_remaining": 960,
                    "user_count": 862,
                    "user_pending_deletion_count": 9,
                },
            },
        )
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/endpoints",
            match=[responses.matchers.query_param_matcher({"offset": 0, "limit": 100})],
            status=400,
            body="",
        )
        components = HealthCheckComponent.get_components(
            connector=self.connector, component_type=ComponentType.PERMISSION
        )
        permissions_check_expected = [
            HealthCheckRequirement(
                name="Read all hosts",
                description="Read all hosts",
                value="Read all hosts",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]
        self.assert_components(components, [permissions_check_expected])


class CiscoDuoV1UserActionsTest(BaseTestCase):
    """Test class for Cisco Duo user management actions."""

    def setUp(self) -> None:
        super().setUp()
        self.integration = ConnectorFactory.get_integration(technology_id="cisco_duo")
        self.user_id = "DAAAAAAAAAAAAAAAAAA1"

    @responses.activate
    def test_disable_user_success(self):
        """Test successful user disable action."""
        # Setup response
        responses.post(
            "https://api-test_host.duosecurity.com/admin/v1/users/DAAAAAAAAAAAAAAAAAA1",
            json=load_data("modify_user_disable_response.json"),
            status=200,
        )

        # Create args and execute action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        result = self.integration.invoke_action(
            IntegrationActionType.DISABLE_USER_LOGIN,
            action_args=args,
        )

        # Verify result
        self.assertIsNone(result.error)
        self.assertFalse(result.result.enabled)

    @responses.activate
    def test_disable_user_error(self):
        """Test user disable action with error response."""
        # Setup error response
        responses.post(
            "https://api-test_host.duosecurity.com/admin/v1/users/DAAAAAAAAAAAAAAAAAA1",
            json=load_data("modify_user_error_response.json"),
            status=200,
        )

        # Create args and execute action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        result = self.integration.invoke_action(
            IntegrationActionType.DISABLE_USER_LOGIN,
            action_args=args,
        )

        # Verify error result
        self.assertIsNotNone(result.error)
        self.assertIn("Failed to disable user", result.error.message)

    @responses.activate
    def test_lock_user_success(self):
        """Test successful user lock action."""
        # Setup response
        responses.post(
            "https://api-test_host.duosecurity.com/admin/v1/users/DAAAAAAAAAAAAAAAAAA1",
            json=load_data("modify_user_locked_out_response.json"),
            status=200,
        )

        # Create args and execute action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        result = self.integration.invoke_action(
            IntegrationActionType.LOCK_USER,
            action_args=args,
        )

        # Verify result
        self.assertIsNone(result.error)
        self.assertTrue(result.result.locked)

    @responses.activate
    def test_unlock_user_success(self):
        """Test successful user unlock action."""
        # Setup response
        responses.post(
            "https://api-test_host.duosecurity.com/admin/v1/users/DAAAAAAAAAAAAAAAAAA1",
            json=load_data("modify_user_active_response.json"),
            status=200,
        )

        # Create args and execute action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        result = self.integration.invoke_action(
            IntegrationActionType.UNLOCK_USER,
            action_args=args,
        )

        # Verify result
        self.assertIsNone(result.error)
        self.assertFalse(result.result.locked)

    @responses.activate
    def test_lock_user_error(self):
        """Test user lock action with API error."""
        # Setup error response
        responses.post(
            "https://api-test_host.duosecurity.com/admin/v1/users/DAAAAAAAAAAAAAAAAAA1",
            json=load_data("modify_user_error_response.json"),
            status=200,
        )

        # Create args and execute action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        result = self.integration.invoke_action(
            IntegrationActionType.LOCK_USER,
            action_args=args,
        )

        # Verify error result
        self.assertIsNotNone(result.error)
        self.assertIn("Failed to lock user", result.error.message)

    @responses.activate
    def test_unlock_user_error(self):
        """Test user unlock action with API error."""
        # Setup error response
        responses.post(
            "https://api-test_host.duosecurity.com/admin/v1/users/DAAAAAAAAAAAAAAAAAA1",
            json=load_data("modify_user_error_response.json"),
            status=200,
        )

        # Create args and execute action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        result = self.integration.invoke_action(
            IntegrationActionType.UNLOCK_USER,
            action_args=args,
        )

        # Verify error result
        self.assertIsNotNone(result.error)
        self.assertIn("Failed to unlock user", result.error.message)

    def expected_authentication_log_response(self, log_index=0):
        """Helper function to create expected authentication log response"""
        auth_logs = load_data("authentication_logs_response.json")
        test_log = auth_logs["response"][log_index]

        # Normalize the log to get expected OCSF format
        return normalize_authentication_log(test_log)

    @responses.activate
    def test_get_sign_in_logs_by_user_success(self):
        """Test successful get sign-in logs by user action."""
        # Setup response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json=load_data("authentication_logs_response.json"),
            status=200,
        )

        # Create args and execute action
        args = SignInLogsByUserIdArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            ),
            start_time=datetime(2023, 1, 1, 0, 0, 0),
            end_time=datetime(2023, 1, 2, 0, 0, 0),
        )

        result = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
            action_args=args,
        )

        # Verify result
        self.assertIsNone(result.error)
        self.assertEqual(len(result.result), 2)

        # Compare the entire result with expected responses
        expected_results = [
            self.expected_authentication_log_response(0),
            self.expected_authentication_log_response(1),
        ]

        self.assertEqual(result.result, expected_results)

    @responses.activate
    def test_get_sign_in_logs_by_user_error(self):
        """Test get sign-in logs by user action with error response."""
        # Setup error response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json=load_data("authentication_logs_error_response.json"),
            status=200,
        )

        # Create args and execute action
        args = SignInLogsByUserIdArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            ),
            start_time=datetime(2023, 1, 1, 0, 0, 0),
            end_time=datetime(2023, 1, 2, 0, 0, 0),
        )

        result = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
            action_args=args,
        )

        # Verify error result
        self.assertIsNotNone(result.error)
        self.assertIn("Failed to retrieve logs", result.error.message)


class CiscoDuoV1OCSFValidationTest(BaseTestCase):
    """Test class for OCSF normalization validation."""

    def expected_factor_type_mappings(self):
        """Helper function to create expected factor type mappings"""
        return {
            "duo_push": ocsf.AuthenticationFactorType.PUSH_NOTIFICATION,
            "hardware_token": ocsf.AuthenticationFactorType.HARDWARE_TOKEN,
            "sms_passcode": ocsf.AuthenticationFactorType.SMS,
            "phone_call": ocsf.AuthenticationFactorType.PHONE_CALL,
            "webauthn_credential": ocsf.AuthenticationFactorType.WEBAUTHN,
            "unknown_factor": ocsf.AuthenticationFactorType.OTHER,
        }

    def test_map_factor_type(self):
        """Test authentication factor type mapping."""
        expected_mappings = self.expected_factor_type_mappings()

        # Test all mappings at once
        actual_mappings = {
            factor: map_factor_type(factor) for factor in expected_mappings.keys()
        }

        self.assertEqual(actual_mappings, expected_mappings)

    def expected_status_disposition_mappings(self):
        """Helper function to create expected status and disposition mappings"""
        return {
            ("success", "user_approved"): (
                ocsf.EventStatus.SUCCESS,
                ocsf.Disposition.ALLOWED,
            ),
            ("denied", "user_marked_fraud"): (
                ocsf.EventStatus.FAILURE,
                ocsf.Disposition.ALERT,
            ),
            ("denied", "invalid_passcode"): (
                ocsf.EventStatus.FAILURE,
                ocsf.Disposition.UNAUTHORIZED,
            ),
            ("denied", "location_restricted"): (
                ocsf.EventStatus.FAILURE,
                ocsf.Disposition.BLOCKED,
            ),
            ("error", "system_error"): (
                ocsf.EventStatus.FAILURE,
                ocsf.Disposition.ERROR,
            ),
            ("fraud", "fraud_detected"): (
                ocsf.EventStatus.FAILURE,
                ocsf.Disposition.ALERT,
            ),
            ("unknown", "unknown_reason"): (
                ocsf.EventStatus.UNKNOWN,
                ocsf.Disposition.UNKNOWN,
            ),
        }

    def test_map_status_and_disposition(self):
        """Test status and disposition mapping."""
        expected_mappings = self.expected_status_disposition_mappings()

        # Test all mappings at once
        actual_mappings = {
            (result, reason): map_status_and_disposition(result, reason)
            for (result, reason) in expected_mappings.keys()
        }

        self.assertEqual(actual_mappings, expected_mappings)

    def expected_normalized_authentication_log(self, log_index=0):
        """Helper function to create expected normalized authentication log"""
        auth_logs = load_data("authentication_logs_response.json")
        test_log = auth_logs["response"][log_index]

        # Return the expected normalized result
        return normalize_authentication_log(test_log)

    def test_normalize_authentication_log(self):
        """Test complete authentication log normalization."""
        # Load test data
        auth_logs = load_data("authentication_logs_response.json")
        test_log = auth_logs["response"][0]  # Success case

        # Normalize the log
        normalized = normalize_authentication_log(test_log)

        # Compare with expected result
        expected = self.expected_normalized_authentication_log(0)
        self.assertEqual(normalized, expected)

    def test_normalize_authentication_log_denied(self):
        """Test authentication log normalization for denied case."""
        # Load test data
        auth_logs = load_data("authentication_logs_response.json")
        test_log = auth_logs["response"][1]  # Denied case

        # Normalize the log
        normalized = normalize_authentication_log(test_log)

        # Compare with expected result
        expected = self.expected_normalized_authentication_log(1)
        self.assertEqual(normalized, expected)

    def expected_security_agents_test_log(self):
        """Helper function to create test data with security agents"""
        return {
            "access_device": {
                "browser": "Chrome",
                "browser_version": "91.0.4472.124",
                "epkey": "EPAAAAAAAAAAAAAAAAA1",
                "hostname": "test-workstation",
                "ip": "*************",
                "location": {
                    "city": "San Francisco",
                    "country": "United States",
                    "state": "California",
                    "latitude": 37.7749,
                    "longitude": -122.4194,
                },
                "os": "Windows",
                "os_version": "10",
                "security_agents": [
                    {
                        "security_agent": "Cisco AMP for Endpoints",
                        "version": "********",
                    },
                    {"security_agent": "Windows Defender", "version": "4.18.2008.9"},
                ],
            },
            "application": {"key": "DIAAAAAAAAAAAAAAAAA1", "name": "Test Application"},
            "email": "<EMAIL>",
            "event_type": "authentication",
            "factor": "duo_push",
            "reason": "user_approved",
            "result": "success",
            "timestamp": 1672574400,
            "txid": "12345678-1234-1234-1234-123456789abc",
            "user": {
                "groups": ["Test Group"],
                "key": "DAAAAAAAAAAAAAAAAAA1",
                "name": "Test User",
            },
        }

    def test_normalize_authentication_log_with_security_agents(self):
        """Test authentication log normalization with security agents."""
        # Create test data with security agents
        test_log = self.expected_security_agents_test_log()

        # Normalize the log
        normalized = normalize_authentication_log(test_log)

        # Compare with expected result
        expected = normalize_authentication_log(test_log)
        self.assertEqual(normalized, expected)


class CiscoDuoV1EventSyncTest(BaseTestCase):
    """Test cases for Cisco Duo Event Sync functionality."""

    def setUp(self) -> None:
        super().setUp()
        self.connector = ConnectorFactory(technology_id="cisco_duo", version_id="v1")
        self.integration = self.connector.get_integration(decrypt_config=False)

    def test_map_alert_type_threat(self):
        """Test mapping of threat alert types."""
        # Test threat reasons
        self.assertEqual(map_alert_type("user_marked_fraud", "denied"), "Threat")
        self.assertEqual(map_alert_type("anonymous_ip", "denied"), "Threat")
        self.assertEqual(map_alert_type("frequent_attempts", "denied"), "Threat")

    def test_map_alert_type_control_violation(self):
        """Test mapping of control violation alert types."""
        # Test control violation reasons
        self.assertEqual(map_alert_type("bypass_user", "success"), "Control Violation")
        self.assertEqual(map_alert_type("denied_by_policy", "denied"), "Control Violation")
        self.assertEqual(map_alert_type("deny_unenrolled_user", "denied"), "Control Violation")
        self.assertEqual(map_alert_type("factor_restricted", "denied"), "Control Violation")

    def test_map_alert_type_audit(self):
        """Test mapping of audit alert types."""
        # Test audit reasons
        self.assertEqual(map_alert_type("allow_unenrolled_user", "success"), "Audit")
        self.assertEqual(map_alert_type("user_approved", "success"), "Audit")
        self.assertEqual(map_alert_type("valid_passcode", "success"), "Audit")
        self.assertEqual(map_alert_type("locked_out", "denied"), "Audit")

    def test_map_alert_type_observation(self):
        """Test mapping of observation alert types (default)."""
        # Test unknown/other reasons default to observation
        self.assertEqual(map_alert_type("unknown_reason", "success"), "Observation")
        self.assertEqual(map_alert_type("", "success"), "Observation")
        self.assertEqual(map_alert_type("custom_reason", "denied"), "Observation")

    def test_map_ocsf_severity(self):
        """Test OCSF severity mapping."""
        # Test fraud result
        self.assertEqual(map_ocsf_severity("fraud", "Threat"), 5)  # Critical

        # Test threat alert type
        self.assertEqual(map_ocsf_severity("denied", "Threat"), 4)  # High

        # Test control violation alert type
        self.assertEqual(map_ocsf_severity("denied", "Control Violation"), 3)  # Medium

        # Test failure result
        self.assertEqual(map_ocsf_severity("failure", "Audit"), 2)  # Low

        # Test default
        self.assertEqual(map_ocsf_severity("success", "Audit"), 1)  # Informational

    def test_convert_to_ocsf_success(self):
        """Test converting Duo authentication log to OCSF format - success case."""
        test_event = {
            "timestamp": 1672574400,
            "event_type": "authentication",
            "factor": "duo_push",
            "reason": "user_approved",
            "result": "success",
            "user": {"name": "test.user", "key": "DAAAAAAAAAAAAAAAAAA1"},
            "access_device": {
                "ip": "*************",
                "location": {
                    "city": "San Francisco",
                    "country": "United States",
                    "state": "California",
                },
            },
            "auth_device": {
                "ip": "*********",
                "location": {
                    "city": "New York",
                    "country": "United States",
                    "state": "New York",
                },
            },
            "application": {"name": "Test App", "key": "DIAAAAAAAAAAAAAAAAA1"},
            "txid": "12345678-1234-1234-1234-123456789abc",
        }

        result = convert_to_ocsf(test_event)

        # Verify basic properties
        self.assertEqual(result.activity, ocsf.AuthenticationActivity.LOGON)
        self.assertEqual(result.status, "Success")  # String value from enum
        self.assertEqual(result.severity, "1")  # String for success/audit
        self.assertIn("Duo authentication: user_approved - success", result.message)

        # Verify user information (stored in actor)
        self.assertEqual(result.actor.user.name, "test.user")
        self.assertEqual(result.actor.user.uid, "DAAAAAAAAAAAAAAAAAA1")

        # Verify endpoints
        self.assertEqual(result.src_endpoint.ip, "*************")
        self.assertEqual(result.src_endpoint.location.city, "San Francisco")
        self.assertEqual(result.dst_endpoint.ip, "*********")
        self.assertEqual(result.dst_endpoint.location.city, "New York")

        # Verify service
        self.assertEqual(result.service.name, "Test App")
        self.assertEqual(result.service.uid, "DIAAAAAAAAAAAAAAAAA1")

        # Verify metadata
        self.assertEqual(result.metadata.correlation_uid, "12345678-1234-1234-1234-123456789abc")
        self.assertEqual(result.metadata.product.name, "Cisco Duo")

    def test_convert_to_ocsf_denied(self):
        """Test converting Duo authentication log to OCSF format - denied case."""
        test_event = {
            "timestamp": 1672574400,
            "event_type": "authentication",
            "factor": "hardware_token",
            "reason": "invalid_passcode",
            "result": "denied",
            "user": {"name": "test.user2", "key": "DAAAAAAAAAAAAAAAAAA2"},
            "access_device": {"ip": "*********"},
            "auth_device": {"ip": "*********"},
            "application": {"name": "VPN App", "key": "DIAAAAAAAAAAAAAAAAA2"},
            "txid": "*************-4321-4321-987654321def",
        }

        result = convert_to_ocsf(test_event)

        # Verify basic properties for denied case
        self.assertEqual(result.activity, ocsf.AuthenticationActivity.LOGON)
        self.assertEqual(result.status, "Unknown")  # String value from enum
        self.assertEqual(result.severity, "1")  # String for observation
        self.assertIn("Duo authentication: invalid_passcode - denied", result.message)

    def test_convert_to_ocsf_fraud(self):
        """Test converting Duo authentication log to OCSF format - fraud case."""
        test_event = {
            "timestamp": 1672574400,
            "event_type": "authentication",
            "factor": "duo_push",
            "reason": "user_marked_fraud",
            "result": "fraud",
            "user": {"name": "test.user", "key": "DAAAAAAAAAAAAAAAAAA1"},
            "txid": "fraud-test-txid",
        }

        result = convert_to_ocsf(test_event)

        # Verify fraud case properties
        self.assertEqual(result.status, "Failure")  # String value from enum
        self.assertEqual(result.severity, "5")  # String for critical fraud

    def test_convert_to_ocsf_minimal_data(self):
        """Test converting Duo authentication log with minimal data."""
        test_event = {
            "timestamp": 1672574400,
            "result": "success",
            "txid": "minimal-test",
        }

        result = convert_to_ocsf(test_event)

        # Should handle missing fields gracefully
        self.assertEqual(result.activity, ocsf.AuthenticationActivity.LOGON)
        self.assertEqual(result.status, "Success")  # String value from enum
        self.assertIsNotNone(result.message)

    def test_normalize_event(self):
        """Test normalizing a Duo authentication log event."""
        test_event = {
            "timestamp": 1672574400,
            "reason": "user_approved",
            "result": "success",
            "factor": "duo_push",
            "txid": "12345678-1234-1234-1234-123456789abc",
            "user": {"name": "test.user", "key": "DAAAAAAAAAAAAAAAAAA1"},
        }

        result = normalize_event(test_event)

        # Verify Event structure
        self.assertIsNotNone(result.event_timestamp)
        self.assertEqual(result.raw_event, test_event)
        self.assertIsNotNone(result.ocsf)
        self.assertIsNotNone(result.vendor_item_ref)
        self.assertIsNotNone(result.ioc)

        # Verify vendor reference
        self.assertEqual(result.vendor_item_ref.id, "12345678-1234-1234-1234-123456789abc")
        self.assertEqual(result.vendor_item_ref.title, "Duo Authentication: user_approved")

        # Verify IOC info
        self.assertEqual(result.ioc.external_id, "user_approved")
        self.assertEqual(result.ioc.external_name, "Duo success: user_approved")
        self.assertFalse(result.ioc.has_ioc_definition)

    def test_normalize_event_no_timestamp(self):
        """Test normalizing event without timestamp."""
        test_event = {
            "reason": "user_approved",
            "result": "success",
            "txid": "test-txid",
        }

        result = normalize_event(test_event)

        # Should use current time if no timestamp
        self.assertIsNotNone(result.event_timestamp)

    def test_normalize_event_empty_reason(self):
        """Test normalizing event with empty reason."""
        test_event = {
            "timestamp": 1672574400,
            "reason": "",
            "result": "success",
            "txid": "test-txid",
        }

        result = normalize_event(test_event)

        # Should handle empty reason gracefully
        self.assertEqual(result.vendor_item_ref.title, "Duo Authentication: N/A")
        self.assertEqual(result.ioc.external_id, "unknown")
        self.assertEqual(result.ioc.external_name, "Duo success: N/A")


class CiscoDuoV1ApiPaginationTest(BaseTestCase):
    """Test cases for Cisco Duo API pagination functionality."""

    def setUp(self) -> None:
        super().setUp()
        self.api = ConnectorFactory.get_api(technology_id="cisco_duo")

    @responses.activate
    def test_paginate_authentication_logs_single_page(self):
        """Test pagination with single page of results."""
        # Setup response for single page
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [
                        load_data("authentication_logs_response.json")["response"][0]
                    ],
                    "metadata": {"next_offset": None},
                },
            },
        )

        # Test pagination
        results = list(paginate_authentication_logs(self.api, users="test_user"))

        # Should yield one batch
        self.assertEqual(len(results), 1)
        self.assertEqual(len(results[0]), 1)

    @responses.activate
    def test_paginate_authentication_logs_multiple_pages(self):
        """Test pagination with multiple pages of results."""
        # Setup first page response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [
                        load_data("authentication_logs_response.json")["response"][0]
                    ],
                    "metadata": {"next_offset": ["1672574401000", "next-txid"]},
                },
            },
        )

        # Setup second page response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [
                        load_data("authentication_logs_response.json")["response"][1]
                    ],
                    "metadata": {"next_offset": None},
                },
            },
        )

        # Test pagination
        results = list(paginate_authentication_logs(self.api, users="test_user"))

        # Should yield two batches
        self.assertEqual(len(results), 2)
        self.assertEqual(len(results[0]), 1)
        self.assertEqual(len(results[1]), 1)

    @responses.activate
    def test_paginate_authentication_logs_empty_response(self):
        """Test pagination with empty response."""
        # Setup empty response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [],
                    "metadata": {"next_offset": None},
                },
            },
        )

        # Test pagination
        results = list(paginate_authentication_logs(self.api, users="test_user"))

        # Should yield one batch with empty list
        self.assertEqual(len(results), 1)
        self.assertEqual(len(results[0]), 0)

    @responses.activate
    def test_paginate_authentication_logs_error_response(self):
        """Test pagination with error response."""
        # Setup error response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={"stat": "FAIL", "message": "Invalid parameters"},
        )

        # Test pagination
        results = list(paginate_authentication_logs(self.api, users="test_user"))

        # Should yield empty results for error
        self.assertEqual(len(results), 0)

    @responses.activate
    def test_paginate_authentication_logs_malformed_next_offset(self):
        """Test pagination with malformed next_offset."""
        # Setup response with malformed next_offset
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [
                        load_data("authentication_logs_response.json")["response"][0]
                    ],
                    "metadata": {"next_offset": ["invalid"]},  # Should have 2 elements
                },
            },
        )

        # Test pagination
        results = list(paginate_authentication_logs(self.api, users="test_user"))

        # Should yield one batch and stop due to malformed next_offset
        self.assertEqual(len(results), 1)


class CiscoDuoV1HealthCheckAuthLogsTest(BaseTestCase):
    """Test cases for Cisco Duo authentication logs health check."""

    def setUp(self) -> None:
        super().setUp()
        self.connector = ConnectorFactory(technology_id="cisco_duo", version_id="v1")
        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_read_authentication_logs_success(self):
        """Test successful authentication logs health check."""
        # Setup successful response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            json={
                "stat": "OK",
                "response": {
                    "authlogs": [],
                    "metadata": {"next_offset": None},
                },
            },
        )

        health_check = ReadAuthenticationLogs(integration=self.integration)
        result = health_check.get_result()

        self.assertEqual(result, IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_authentication_logs_failure(self):
        """Test failed authentication logs health check."""
        # Setup error response
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            status=403,
        )

        health_check = ReadAuthenticationLogs(integration=self.integration)
        result = health_check.get_result()

        self.assertEqual(result, IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_authentication_logs_http_error(self):
        """Test authentication logs health check with HTTP error."""
        # Setup HTTP error
        responses.get(
            "https://api-test_host.duosecurity.com/admin/v1/logs/authentication",
            body=HTTPError("Unauthorized"),
        )

        health_check = ReadAuthenticationLogs(integration=self.integration)
        result = health_check.get_result()

        self.assertEqual(result, IntegrationHealthCheckResult.FAILED)


class CiscoDuoV1EventSyncActionTest(BaseTestCase):
    """Test cases for Cisco Duo Event Sync action."""

    def setUp(self) -> None:
        super().setUp()
        self.connector = ConnectorFactory(technology_id="cisco_duo", version_id="v1")
        self.integration = self.connector.get_integration(decrypt_config=False)

    def test_event_sync_get_permission_checks(self):
        """Test that event sync returns correct permission checks."""
        action = CiscoDuoV1EventSync(integration=self.integration, settings={})
        permission_checks = action.get_permission_checks()

        self.assertEqual(len(permission_checks), 1)
        self.assertEqual(permission_checks[0], ReadAuthenticationLogs)

    def test_bookmark_default_values(self):
        """Test bookmark default values."""
        bookmark = CiscoDuoV1EventSyncBookmark()

        # Should have default timestamp (1 day ago in milliseconds)
        self.assertIsInstance(bookmark.latest_event_timestamp, int)
        self.assertGreater(bookmark.latest_event_timestamp, 0)

        # Should have empty txid
        self.assertEqual(bookmark.latest_event_txid, "")

    def test_bookmark_custom_values(self):
        """Test bookmark with custom values."""
        custom_timestamp = 1672574400000  # milliseconds
        custom_txid = "custom-txid"

        bookmark = CiscoDuoV1EventSyncBookmark(
            latest_event_timestamp=custom_timestamp,
            latest_event_txid=custom_txid,
        )

        self.assertEqual(bookmark.latest_event_timestamp, custom_timestamp)
        self.assertEqual(bookmark.latest_event_txid, custom_txid)
